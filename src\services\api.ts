import { GoogleGenerativeAI } from '@google/generative-ai';
import { Quiz, VideoSummary } from '@/types';
import { extractVideoId } from '@/utils/youtube';

// Initialize Google Gemini AI
const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GOOGLE_API_KEY || '');

// Test API connection
console.log('🤖 Gemini AI initialized with API key:', import.meta.env.VITE_GOOGLE_API_KEY ? '✅ Configured' : '❌ Missing');

// YouTube Data API for getting video transcripts
const YOUTUBE_API_KEY = import.meta.env.VITE_YOUTUBE_API_KEY || '';

interface YouTubeVideoDetails {
  title: string;
  description: string;
  duration: string;
  transcript?: string;
}

/**
 * Get video details from YouTube Data API
 */
async function getYouTubeVideoDetails(videoId: string): Promise<YouTubeVideoDetails> {
  try {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${YOUTUBE_API_KEY}&part=snippet,contentDetails`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch video details');
    }

    const data = await response.json();

    if (!data.items || data.items.length === 0) {
      throw new Error('Video not found');
    }

    const video = data.items[0];

    return {
      title: video.snippet.title,
      description: video.snippet.description,
      duration: formatDuration(video.contentDetails.duration),
    };
  } catch (error) {
    console.error('Error fetching video details:', error);
    // Return mock data if API fails
    return {
      title: "Sample Video Title",
      description: "This is a sample video description for demonstration purposes.",
      duration: "15:30"
    };
  }
}

/**
 * Convert YouTube duration format (PT15M30S) to readable format (15:30)
 */
function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return "0:00";

  const hours = parseInt(match[1] || "0");
  const minutes = parseInt(match[2] || "0");
  const seconds = parseInt(match[3] || "0");

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// Mock data for demonstration when API is not available
const mockVideoSummaries: Record<string, VideoSummary> = {
  default: {
    title: "Understanding React Hooks",
    duration: "15:30",
    summary: "This video provides a comprehensive introduction to React Hooks, covering useState, useEffect, and custom hooks. The presenter explains how hooks revolutionized React development by allowing functional components to manage state and side effects. Key concepts include the rules of hooks, common patterns, and best practices for building maintainable React applications.",
    keyPoints: [
      "React Hooks allow functional components to use state and lifecycle methods",
      "useState manages component state in functional components",
      "useEffect handles side effects and replaces lifecycle methods",
      "Custom hooks enable reusable stateful logic",
      "Rules of hooks must be followed for proper functionality"
    ]
  }
};

const mockQuizzes: Record<string, Quiz> = {
  default: {
    id: "quiz-1",
    title: "React Hooks Quiz",
    description: "Test your understanding of React Hooks concepts",
    difficulty: "medium",
    estimatedTime: 10,
    questions: [
      {
        id: "q1",
        question: "What is the primary purpose of React Hooks?",
        options: [
          "To replace class components entirely",
          "To allow functional components to use state and lifecycle features",
          "To improve performance of React applications",
          "To simplify component styling"
        ],
        correctAnswer: 1,
        explanation: "React Hooks allow functional components to use state and other React features that were previously only available in class components."
      },
      {
        id: "q2",
        question: "Which hook is used to manage state in functional components?",
        options: [
          "useEffect",
          "useState",
          "useContext",
          "useReducer"
        ],
        correctAnswer: 1,
        explanation: "useState is the hook specifically designed for managing state in functional components."
      },
      {
        id: "q3",
        question: "What is the first rule of hooks?",
        options: [
          "Only call hooks from React functions",
          "Always call hooks in the same order",
          "Don't call hooks inside loops, conditions, or nested functions",
          "Both A and C are correct"
        ],
        correctAnswer: 3,
        explanation: "The rules of hooks state that you should only call hooks from React functions and never call them inside loops, conditions, or nested functions."
      },
      {
        id: "q4",
        question: "Which hook is used to perform side effects?",
        options: [
          "useState",
          "useEffect",
          "useMemo",
          "useCallback"
        ],
        correctAnswer: 1,
        explanation: "useEffect is used to perform side effects in functional components, such as data fetching, subscriptions, or manually changing the DOM."
      },
      {
        id: "q5",
        question: "What does useEffect with an empty dependency array do?",
        options: [
          "Runs on every render",
          "Runs only once after the initial render",
          "Never runs",
          "Runs only when state changes"
        ],
        correctAnswer: 1,
        explanation: "useEffect with an empty dependency array runs only once after the initial render, similar to componentDidMount in class components."
      }
    ]
  }
};

/**
 * Generate quiz and summary from YouTube video using Google Gemini AI
 */
export async function generateQuizFromVideo(
  videoUrl: string,
  difficulty: 'easy' | 'medium' | 'hard' = 'medium',
  questionCount: number = 5
): Promise<{ quiz: Quiz; summary: VideoSummary }> {
  const videoId = extractVideoId(videoUrl);

  if (!videoId) {
    throw new Error('Invalid YouTube URL');
  }

  try {
    // Get video details from YouTube API
    const videoDetails = await getYouTubeVideoDetails(videoId);

    // Check if we have API keys configured
    const hasGeminiKey = import.meta.env.VITE_GOOGLE_API_KEY;

    if (!hasGeminiKey) {
      console.warn('Google API key not configured, using mock data');
      return generateMockQuizAndSummary(videoDetails, difficulty, questionCount);
    }

    // Generate content using Gemini AI (Flash 2.5) with enhanced configuration
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-exp",
      generationConfig: {
        temperature: 0.7, // Balanced creativity and consistency
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 4096,
      },
    });

    const prompt = `
    You are an expert educational content creator. Analyze this YouTube video and create high-quality educational content:

    **Video Information:**
    - Title: ${videoDetails.title}
    - Description: ${videoDetails.description}
    - Duration: ${videoDetails.duration}

    **Task:** Create comprehensive educational content with the following specifications:

    1. **Summary (2-3 paragraphs):** Write a detailed, engaging summary that captures the main concepts, key insights, and practical applications discussed in the video.

    2. **Quiz Questions (${questionCount} questions):** Create ${questionCount} thoughtful multiple-choice questions that:
       - Test understanding of core concepts
       - Are appropriate for ${difficulty} difficulty level
       - Include 4 plausible options each
       - Have clear, educational explanations
       - Progress from basic recall to application/analysis

    3. **Key Learning Points (5-7 points):** Extract the most important takeaways that learners should remember.

    **Difficulty Guidelines:**
    - Easy: Basic recall and comprehension
    - Medium: Application and analysis of concepts
    - Hard: Synthesis, evaluation, and complex problem-solving

    **Output Format:** Respond with valid JSON only:
    {
      "summary": "comprehensive 2-3 paragraph summary",
      "keyPoints": ["actionable learning point 1", "key insight 2", "practical application 3", ...],
      "questions": [
        {
          "question": "clear, specific question text",
          "options": ["option A", "option B", "option C", "option D"],
          "correctAnswer": 0,
          "explanation": "detailed explanation of why this answer is correct and why others are wrong"
        }
      ]
    }

    Ensure all content is educational, accurate, and directly relevant to the video's subject matter.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Parse the AI response - handle various JSON formatting
    let cleanedText = text.trim();

    // Remove markdown code blocks if present
    cleanedText = cleanedText.replace(/```json\n?|\n?```/g, '');
    cleanedText = cleanedText.replace(/```\n?|\n?```/g, '');

    // Find JSON content between braces
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in AI response');
    }

    const aiContent = JSON.parse(jsonMatch[0]);

    // Create quiz object
    const quiz: Quiz = {
      id: `quiz-${Date.now()}`,
      title: `Quiz: ${videoDetails.title}`,
      description: `Test your understanding of this video content`,
      difficulty,
      estimatedTime: Math.ceil(questionCount * 1.5),
      questions: aiContent.questions.map((q: any, index: number) => ({
        id: `q${index + 1}`,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation
      }))
    };

    // Create summary object
    const summary: VideoSummary = {
      title: videoDetails.title,
      duration: videoDetails.duration,
      summary: aiContent.summary,
      keyPoints: aiContent.keyPoints
    };

    return { quiz, summary };

  } catch (error) {
    console.error('Error generating quiz with AI:', error);

    // Fallback to mock data if AI fails
    const videoDetails = await getYouTubeVideoDetails(videoId);
    return generateMockQuizAndSummary(videoDetails, difficulty, questionCount);
  }
}

/**
 * Generate mock quiz and summary as fallback
 */
function generateMockQuizAndSummary(
  videoDetails: YouTubeVideoDetails,
  difficulty: 'easy' | 'medium' | 'hard',
  questionCount: number
): { quiz: Quiz; summary: VideoSummary } {
  const baseQuiz = mockQuizzes.default;
  const baseSummary = mockVideoSummaries.default;

  // Adjust quiz based on difficulty and question count
  const questions = baseQuiz.questions.slice(0, questionCount);

  const quiz: Quiz = {
    ...baseQuiz,
    id: `quiz-${Date.now()}`,
    title: `Quiz: ${videoDetails.title}`,
    difficulty,
    questions,
    estimatedTime: Math.ceil(questionCount * 1.5)
  };

  const summary: VideoSummary = {
    title: videoDetails.title,
    duration: videoDetails.duration,
    summary: `This video "${videoDetails.title}" covers important concepts and provides valuable insights. ${baseSummary.summary}`,
    keyPoints: baseSummary.keyPoints
  };

  return { quiz, summary };
}

/**
 * Simulates API call to get video summary only
 */
export async function getVideoSummary(videoUrl: string): Promise<VideoSummary> {
  await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));

  const videoId = extractVideoId(videoUrl);
  return {
    ...mockVideoSummaries.default,
    title: `Video Summary - ${videoId || 'Sample Video'}`
  };
}
