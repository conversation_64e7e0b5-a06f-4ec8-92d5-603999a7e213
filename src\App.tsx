import { useState } from 'react';
import { generateQuizFromVideo } from '@/services/api';
import type { Quiz, VideoSummary } from '@/types';

// YouTube URL validation
function isValidYouTubeUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  const patterns = [
    /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+/,
    /^https?:\/\/(www\.)?youtu\.be\/[\w-]+/,
  ];
  return patterns.some(pattern => pattern.test(url));
}

// Hero Icons (simple SVG components)
const PlayIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
  </svg>
);

const BrainIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
  </svg>
);

const DocumentIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const SparklesIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
  </svg>
);

const CheckIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
);



function App() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'input' | 'quiz' | 'results'>('input');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, number>>({});
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [videoSummary, setVideoSummary] = useState<VideoSummary | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!url.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    if (!isValidYouTubeUrl(url)) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    setIsLoading(true);

    try {
      const { quiz: generatedQuiz, summary } = await generateQuizFromVideo(url, 'medium', 5);
      setQuiz(generatedQuiz);
      setVideoSummary(summary);
      setCurrentStep('quiz');
    } catch (err) {
      console.error('Error generating quiz:', err);
      setError('Failed to generate quiz. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
    if (error) setError('');
  };

  const handleAnswerSelect = (questionId: string, answerIndex: number) => {
    setAnswers(prev => ({ ...prev, [questionId]: answerIndex }));
  };

  const handleNext = () => {
    if (!quiz) return;

    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      // Quiz completed
      setCurrentStep('results');
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleBackToInput = () => {
    setCurrentStep('input');
    setCurrentQuestionIndex(0);
    setAnswers({});
    setQuiz(null);
    setVideoSummary(null);
    setUrl('');
    setError('');
  };

  // Get current question and calculate score
  const currentQuestion = quiz?.questions[currentQuestionIndex];
  const score = quiz ? Object.entries(answers).reduce((acc, [questionId, answerIndex]) => {
    const question = quiz.questions.find(q => q.id === questionId);
    return acc + (question && question.correctAnswer === answerIndex ? 1 : 0);
  }, 0) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {currentStep === 'input' && (
        <>
          {/* Hero Section */}
          <div className="relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

            {/* Navigation */}
            <nav className="relative z-10 px-6 py-4">
              <div className="max-w-7xl mx-auto flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                    <PlayIcon />
                  </div>
                  <span className="text-xl font-bold">QuizGen</span>
                </div>
                <div className="hidden md:flex items-center space-x-6">
                  <a href="#features" className="text-gray-600 hover:text-black transition-colors">Features</a>
                  <a href="#how-it-works" className="text-gray-600 hover:text-black transition-colors">How it works</a>
                  <button className="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors">
                    Get Started
                  </button>
                </div>
              </div>
            </nav>

            {/* Hero Content */}
            <div className="relative z-10 px-6 py-20">
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-flex items-center space-x-2 bg-gray-100 rounded-full px-4 py-2 mb-8">
                  <SparklesIcon />
                  <span className="text-sm font-medium">Powered by Google Gemini 2.5 Flash</span>
                </div>

                <h1 className="text-5xl md:text-7xl font-bold tracking-tight mb-6">
                  Transform
                  <span className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent"> YouTube </span>
                  into Learning
                </h1>

                <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
                  Generate interactive quizzes and comprehensive summaries from any YouTube video using advanced AI.
                  Perfect for students, educators, and lifelong learners.
                </p>

                {/* Input Section */}
                <div className="max-w-2xl mx-auto">
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="relative">
                      <input
                        id="youtube-url"
                        type="url"
                        placeholder="Paste your YouTube URL here..."
                        value={url}
                        onChange={handleUrlChange}
                        className={`w-full px-6 py-4 text-lg border-2 rounded-2xl focus:outline-none focus:ring-4 focus:ring-black/10 focus:border-black transition-all ${
                          error ? 'border-red-500' : 'border-gray-200'
                        } bg-white shadow-lg`}
                        disabled={isLoading}
                      />
                      {error && (
                        <p className="text-sm text-red-600 mt-2 text-left">{error}</p>
                      )}
                    </div>

                    <button
                      type="submit"
                      disabled={isLoading || !url.trim()}
                      className="w-full bg-black text-white py-4 px-8 rounded-2xl hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-2">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Generating Quiz...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center space-x-2">
                          <BrainIcon />
                          <span>Generate Quiz & Summary</span>
                        </div>
                      )}
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>

          {/* Features Section */}
          <div id="features" className="py-20 px-6">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold mb-4">Why Choose QuizGen?</h2>
                <p className="text-xl text-gray-600">Powerful features to enhance your learning experience</p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-shadow">
                  <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <BrainIcon />
                  </div>
                  <h3 className="text-xl font-bold mb-4">AI-Powered Analysis</h3>
                  <p className="text-gray-600">Advanced AI understands video content and generates relevant, challenging questions automatically.</p>
                </div>

                <div className="text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-shadow">
                  <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <DocumentIcon />
                  </div>
                  <h3 className="text-xl font-bold mb-4">Comprehensive Summaries</h3>
                  <p className="text-gray-600">Get detailed summaries with key points, making it easy to review and retain information.</p>
                </div>

                <div className="text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-shadow">
                  <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <SparklesIcon />
                  </div>
                  <h3 className="text-xl font-bold mb-4">Interactive Learning</h3>
                  <p className="text-gray-600">Engage with content through interactive quizzes that test your understanding and retention.</p>
                </div>
              </div>
            </div>
          </div>

          {/* How it Works Section */}
          <div id="how-it-works" className="py-20 px-6 bg-gray-50">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold mb-4">How It Works</h2>
                <p className="text-xl text-gray-600">Simple steps to transform any video into a learning experience</p>
              </div>

              <div className="space-y-8">
                <div className="flex items-center space-x-6">
                  <div className="w-12 h-12 bg-black text-white rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0">1</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Paste YouTube URL</h3>
                    <p className="text-gray-600">Simply copy and paste any YouTube video link into our input field.</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="w-12 h-12 bg-black text-white rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0">2</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">AI Analysis</h3>
                    <p className="text-gray-600">Our AI analyzes the video content and generates relevant quiz questions and summaries.</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="w-12 h-12 bg-black text-white rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0">3</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Learn & Test</h3>
                    <p className="text-gray-600">Take the interactive quiz and review the comprehensive summary to reinforce your learning.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
              <div className="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
                <div className="w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
                <h3 className="text-xl font-bold mb-2">Analyzing Video Content</h3>
                <p className="text-gray-600 mb-4">Our AI is processing the video and generating your personalized quiz...</p>
                <div className="bg-gray-100 rounded-full h-2 overflow-hidden">
                  <div className="bg-black h-full rounded-full animate-pulse" style={{width: '60%'}}></div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {currentStep === 'quiz' && quiz && currentQuestion && (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white px-6 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-2">{quiz.title}</h1>
              <p className="text-gray-600">
                Question {currentQuestionIndex + 1} of {quiz.questions.length}
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-4">
                <div
                  className="bg-black h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentQuestionIndex + 1) / quiz.questions.length) * 100}%` }}
                />
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
              <h3 className="text-2xl font-semibold mb-8">{currentQuestion.question}</h3>

              <div className="space-y-4">
                {currentQuestion.options.map((option, index) => (
                  <label
                    key={index}
                    className={`flex items-center p-4 border-2 rounded-xl cursor-pointer hover:bg-gray-50 transition-all ${
                      answers[currentQuestion.id] === index
                        ? 'border-black bg-gray-50'
                        : 'border-gray-200'
                    }`}
                  >
                    <input
                      type="radio"
                      name={`question-${currentQuestion.id}`}
                      value={index}
                      checked={answers[currentQuestion.id] === index}
                      onChange={() => handleAnswerSelect(currentQuestion.id, index)}
                      className="mr-4 w-4 h-4"
                    />
                    <span className="font-semibold mr-3 text-lg">{String.fromCharCode(65 + index)}.</span>
                    <span className="text-lg">{option}</span>
                  </label>
                ))}
              </div>

              <div className="flex justify-between mt-8">
                <button
                  type="button"
                  onClick={handlePrevious}
                  disabled={currentQuestionIndex === 0}
                  className="px-6 py-3 border-2 border-gray-300 rounded-xl hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  Previous
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  disabled={answers[currentQuestion.id] === undefined}
                  className="px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all"
                >
                  {currentQuestionIndex === quiz.questions.length - 1 ? 'Finish Quiz' : 'Next Question'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {currentStep === 'results' && quiz && videoSummary && (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white px-6 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold mb-6">Quiz Complete! 🎉</h1>
              <div className="inline-flex items-center justify-center w-32 h-32 bg-black text-white rounded-full text-4xl font-bold mb-6">
                {score}/{quiz.questions.length}
              </div>
              <p className="text-2xl text-gray-600">
                You scored {Math.round((score / quiz.questions.length) * 100)}%
              </p>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg mb-8">
              <div className="flex items-center space-x-3 mb-6">
                <DocumentIcon />
                <h3 className="text-2xl font-bold">Video Summary</h3>
              </div>
              <p className="text-gray-700 mb-6 text-lg leading-relaxed">
                {videoSummary.summary}
              </p>
              <div className="space-y-4">
                <h4 className="text-xl font-semibold flex items-center space-x-2">
                  <CheckIcon />
                  <span>Key Points:</span>
                </h4>
                <ul className="space-y-3">
                  {videoSummary.keyPoints.map((point, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700">{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="flex justify-center space-x-4">
              <button
                type="button"
                onClick={handleBackToInput}
                className="px-8 py-3 border-2 border-gray-300 rounded-xl hover:bg-gray-50 transition-all text-lg"
              >
                Generate New Quiz
              </button>
              <button
                type="button"
                onClick={() => {
                  setCurrentStep('quiz');
                  setCurrentQuestionIndex(0);
                  setAnswers({});
                }}
                className="px-8 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all text-lg"
              >
                Retake Quiz
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
